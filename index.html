<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Location-Based AR Backdrop</title>
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #000;
            overflow: hidden;
        }

        #videoContainer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
        }

        #video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: scaleX(-1); /* Mirror the video for natural AR feel */
        }

        a-scene {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 2;
            pointer-events: none;
        }

        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            min-width: 220px;
            pointer-events: auto;
        }

        .controls h3 {
            margin: 0 0 10px 0;
            color: #FF6B6B;
        }

        .control-group {
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            min-width: 60px;
            font-size: 11px;
        }

        .control-group input[type="range"] {
            flex: 1;
            height: 20px;
        }

        .control-group .value {
            min-width: 40px;
            text-align: right;
            font-weight: bold;
            color: #FF6B6B;
        }

        .controls button {
            background: #FF6B6B;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.3s;
        }

        .controls button:hover {
            background: #ff5252;
        }

        .controls button:active {
            transform: scale(0.95);
        }

        .button-row {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }

        #error {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 2000;
            display: none;
        }

        #startButton {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #FF6B6B;
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
        }

        #startButton:hover {
            background: #ff5252;
        }
    </style>
</head>

<body>
    <button id="startButton">Start Location-Based AR</button>

    <div id="videoContainer" style="display: none;">
        <video id="video" autoplay playsinline muted></video>
    </div>

    <!-- A-Frame Scene with Camera-Relative Positioning -->
    <a-scene
        embedded
        style="display: none;"
        id="scene"
        background="color: transparent"
        vr-mode-ui="enabled: false"
        device-orientation-permission-ui="enabled: false">

        <a-assets>
            <a-mixin id="wall-material"
                material="color: #4CC3D9; opacity: 0.8; transparent: true; side: double"></a-mixin>
        </a-assets>

        <!-- Camera at origin (0, 0, 0) - This is our reference point -->
        <a-camera id="camera"
            position="0 0 0"
            look-controls="enabled: false"
            wasd-controls="enabled: false">
        </a-camera>

        <!-- Backdrop Wall positioned exactly 3 meters behind camera -->
        <a-plane id="backdrop-wall"
            mixin="wall-material"
            position="0 0 -3"
            width="6"
            height="4"
            rotation="0 0 0">
        </a-plane>

        <!-- Optional: Floor for reference -->
        <a-plane id="floor"
            position="0 -2 -3"
            rotation="-90 0 0"
            width="8"
            height="8"
            material="color: #333; opacity: 0.3; transparent: true"
            visible="false">
        </a-plane>

        <!-- Lighting -->
        <a-light type="ambient" color="#404040" intensity="0.6"></a-light>
        <a-light type="directional" position="0 2 1" color="#ffffff" intensity="0.8"></a-light>
    </a-scene>

    <div class="controls" style="display: none;" id="controls">
        <h3>📍 Location-Based AR Backdrop</h3>
        <p style="font-size: 10px; color: #ccc; margin: 0 0 10px 0;">
            Camera at origin (0,0,0) • Wall at 3m distance
        </p>

        <div class="control-group">
            <label>Distance:</label>
            <input type="range" id="distance" min="1" max="10" value="3" step="0.1">
            <span class="value" id="distanceValue">3.0m</span>
        </div>

        <div class="control-group">
            <label>Width:</label>
            <input type="range" id="width" min="2" max="15" value="6" step="0.5">
            <span class="value" id="widthValue">6.0m</span>
        </div>

        <div class="control-group">
            <label>Height:</label>
            <input type="range" id="height" min="2" max="10" value="4" step="0.5">
            <span class="value" id="heightValue">4.0m</span>
        </div>

        <div class="control-group">
            <label>Opacity:</label>
            <input type="range" id="opacity" min="0" max="100" value="80" step="5">
            <span class="value" id="opacityValue">80%</span>
        </div>

        <div class="control-group">
            <label>X Offset:</label>
            <input type="range" id="positionX" min="-5" max="5" value="0" step="0.1">
            <span class="value" id="positionXValue">0.0m</span>
        </div>

        <div class="control-group">
            <label>Y Offset:</label>
            <input type="range" id="positionY" min="-3" max="3" value="0" step="0.1">
            <span class="value" id="positionYValue">0.0m</span>
        </div>

        <div class="button-row">
            <button onclick="toggleColor()">🎨 Color</button>
            <button onclick="toggleFloor()">🏠 Floor</button>
            <button onclick="resetWall()">� Reset</button>
            <button onclick="toggleRotation()">🔄 Rotate</button>
        </div>

        <div class="button-row">
            <button onclick="setPreset('close')">📍 Close (1m)</button>
            <button onclick="setPreset('standard')">📏 Standard (3m)</button>
            <button onclick="setPreset('far')">🌅 Far (6m)</button>
        </div>

        <div class="button-row">
            <button onclick="setPreset('wide')">↔️ Wide Wall</button>
            <button onclick="setPreset('tall')">↕️ Tall Wall</button>
            <button onclick="setPreset('room')">🏠 Room Size</button>
        </div>

        <div style="margin-top: 10px; font-size: 10px; color: #ccc;">
            <div>Location-Based AR: Camera = Origin Point</div>
            <div>Shortcuts: C=Color, R=Reset, F=Floor</div>
        </div>
    </div>

    <div id="error">
        <h3>Camera Error</h3>
        <p id="errorMessage"></p>
        <button onclick="hideError()">OK</button>
    </div>

    <script>
        // Global variables for location-based AR
        let video, scene, backdropWall, camera, floor;
        let stream;
        const colors = ['#4CC3D9', '#EF2D5E', '#FFC65D', '#7BC8A4', '#CC7A00', '#8B4A9C', '#FF6B6B'];
        let currentColorIndex = 0;
        let rotationEnabled = false;

        // Get DOM elements
        const startButton = document.getElementById('startButton');
        const videoContainer = document.getElementById('videoContainer');
        const controls = document.getElementById('controls');
        const sceneEl = document.getElementById('scene');

        // Control sliders
        const distanceSlider = document.getElementById('distance');
        const widthSlider = document.getElementById('width');
        const heightSlider = document.getElementById('height');
        const opacitySlider = document.getElementById('opacity');
        const positionXSlider = document.getElementById('positionX');
        const positionYSlider = document.getElementById('positionY');

        // Value displays
        const distanceValue = document.getElementById('distanceValue');
        const widthValue = document.getElementById('widthValue');
        const heightValue = document.getElementById('heightValue');
        const opacityValue = document.getElementById('opacityValue');
        const positionXValue = document.getElementById('positionXValue');
        const positionYValue = document.getElementById('positionYValue');

        // Start camera and initialize location-based AR
        async function startCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    },
                    audio: false
                });

                video = document.getElementById('video');
                video.srcObject = stream;

                video.onloadedmetadata = () => {
                    startButton.style.display = 'none';
                    videoContainer.style.display = 'block';
                    sceneEl.style.display = 'block';
                    controls.style.display = 'block';

                    // Initialize A-Frame elements
                    initLocationBasedAR();
                };

            } catch (err) {
                alert('Could not access camera: ' + err.message);
            }
        }

        // Initialize location-based AR system
        function initLocationBasedAR() {
            scene = document.querySelector('a-scene');
            backdropWall = document.getElementById('backdrop-wall');
            camera = document.getElementById('camera');
            floor = document.getElementById('floor');

            // Wait for scene to load
            if (scene.hasLoaded) {
                setupLocationBasedAR();
            } else {
                scene.addEventListener('loaded', setupLocationBasedAR);
            }
        }

        function setupLocationBasedAR() {
            updateWallPosition();
            setupEventListeners();

            console.log('Location-Based AR initialized:');
            console.log('- Camera at origin (0, 0, 0)');
            console.log('- Wall positioned 3 meters away (0, 0, -3)');
        }

        // Setup event listeners
        function setupEventListeners() {
            distanceSlider.addEventListener('input', updateWallPosition);
            widthSlider.addEventListener('input', updateWallPosition);
            heightSlider.addEventListener('input', updateWallPosition);
            opacitySlider.addEventListener('input', updateWallPosition);
            positionXSlider.addEventListener('input', updateWallPosition);
            positionYSlider.addEventListener('input', updateWallPosition);
        }

        // Update wall position relative to camera (origin)
        function updateWallPosition() {
            if (!backdropWall) return;

            // Get values from controls
            const distance = parseFloat(distanceSlider.value);
            const width = parseFloat(widthSlider.value);
            const height = parseFloat(heightSlider.value);
            const opacity = parseFloat(opacitySlider.value) / 100;
            const offsetX = parseFloat(positionXSlider.value);
            const offsetY = parseFloat(positionYSlider.value);

            // Position wall relative to camera origin (0, 0, 0)
            // Camera looks down negative Z axis, so wall is at -distance
            const wallX = 0 + offsetX;  // X offset from center
            const wallY = 0 + offsetY;  // Y offset from center
            const wallZ = -distance;    // Distance away from camera

            // Update wall properties
            backdropWall.setAttribute('position', `${wallX} ${wallY} ${wallZ}`);
            backdropWall.setAttribute('width', width);
            backdropWall.setAttribute('height', height);
            backdropWall.setAttribute('material', `opacity: ${opacity}; transparent: true; side: double`);

            // Update floor position to match wall distance
            if (floor) {
                floor.setAttribute('position', `0 ${-height/2 - 0.5} ${wallZ}`);
            }

            // Update display values
            distanceValue.textContent = distance.toFixed(1) + 'm';
            widthValue.textContent = width.toFixed(1) + 'm';
            heightValue.textContent = height.toFixed(1) + 'm';
            opacityValue.textContent = Math.round(opacity * 100) + '%';
            positionXValue.textContent = offsetX.toFixed(1) + 'm';
            positionYValue.textContent = offsetY.toFixed(1) + 'm';

            console.log(`Wall positioned at: (${wallX.toFixed(1)}, ${wallY.toFixed(1)}, ${wallZ.toFixed(1)})`);
        }

        // Toggle wall color
        function toggleColor() {
            if (!backdropWall) return;
            currentColorIndex = (currentColorIndex + 1) % colors.length;
            const currentMaterial = backdropWall.getAttribute('material');
            backdropWall.setAttribute('material', `color: ${colors[currentColorIndex]}; opacity: ${currentMaterial.opacity}; transparent: true; side: double`);
        }

        // Toggle floor visibility
        function toggleFloor() {
            if (!floor) return;
            const isVisible = floor.getAttribute('visible');
            floor.setAttribute('visible', isVisible === 'false' ? 'true' : 'false');
        }

        // Toggle rotation animation
        function toggleRotation() {
            if (!backdropWall) return;
            rotationEnabled = !rotationEnabled;

            if (rotationEnabled) {
                backdropWall.setAttribute('animation__rotation', 'property: rotation; to: 0 360 0; dur: 20000; loop: true');
            } else {
                backdropWall.removeAttribute('animation__rotation');
                backdropWall.setAttribute('rotation', '0 0 0');
            }
        }

        // Set presets for different wall configurations
        function setPreset(preset) {
            if (!backdropWall) return;

            switch (preset) {
                case 'close':
                    distanceSlider.value = 1.5;
                    widthSlider.value = 4;
                    heightSlider.value = 3;
                    break;
                case 'standard':
                    distanceSlider.value = 3;
                    widthSlider.value = 6;
                    heightSlider.value = 4;
                    break;
                case 'far':
                    distanceSlider.value = 6;
                    widthSlider.value = 10;
                    heightSlider.value = 6;
                    break;
                case 'wide':
                    distanceSlider.value = 3;
                    widthSlider.value = 12;
                    heightSlider.value = 4;
                    break;
                case 'tall':
                    distanceSlider.value = 3;
                    widthSlider.value = 6;
                    heightSlider.value = 8;
                    break;
                case 'room':
                    distanceSlider.value = 4;
                    widthSlider.value = 8;
                    heightSlider.value = 6;
                    floor.setAttribute('visible', 'true');
                    break;
            }

            // Reset position offsets for presets
            positionXSlider.value = 0;
            positionYSlider.value = 0;

            updateWallPosition();
        }

        // Reset wall to default location-based position
        function resetWall() {
            distanceSlider.value = 3;
            widthSlider.value = 6;
            heightSlider.value = 4;
            opacitySlider.value = 80;
            positionXSlider.value = 0;
            positionYSlider.value = 0;
            currentColorIndex = 0;
            rotationEnabled = false;

            if (backdropWall) {
                backdropWall.removeAttribute('animation__rotation');
                backdropWall.setAttribute('rotation', '0 0 0');
                backdropWall.setAttribute('material', `color: ${colors[0]}; opacity: 0.8; transparent: true; side: double`);
            }

            if (floor) {
                floor.setAttribute('visible', 'false');
            }

            updateWallPosition();
            console.log('Wall reset to standard position: 3 meters from camera origin');
        }

        // Keyboard shortcuts for location-based AR
        document.addEventListener('keydown', function (event) {
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') return;

            switch (event.key.toLowerCase()) {
                case 'c':
                    toggleColor();
                    break;
                case 'f':
                    toggleFloor();
                    break;
                case 'r':
                    resetWall();
                    break;
                case '1':
                    setPreset('close');
                    break;
                case '2':
                    setPreset('standard');
                    break;
                case '3':
                    setPreset('far');
                    break;
            }
        });

        // Initialize event listeners when page loads
        startButton.addEventListener('click', startCamera);

        // Cleanup on page unload
        window.addEventListener('beforeunload', function () {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
        });
    </script>
</body>

</html>