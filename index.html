<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Webcam Backdrop Rectangle</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #000;
            overflow: hidden;
        }

        #videoContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        #backdrop {
            position: absolute;
            border: 3px solid #FF6B6B;
            background: rgba(76, 195, 217, 0.8);
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            transition: all 0.3s ease;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        #backdrop::before {
            content: 'BACKDROP ACTIVE';
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            white-space: nowrap;
        }

        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            min-width: 200px;
        }

        .controls h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }

        .control-group {
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            min-width: 60px;
            font-size: 11px;
        }

        .control-group input[type="range"] {
            flex: 1;
            height: 20px;
        }

        .control-group .value {
            min-width: 40px;
            text-align: right;
            font-weight: bold;
            color: #4CAF50;
        }

        .controls button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.3s;
        }

        .controls button:hover {
            background: #45a049;
        }

        .controls button:active {
            transform: scale(0.95);
        }

        .button-row {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }

        #error {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 2000;
            display: none;
        }

        #startButton {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #4CAF50;
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
        }

        #startButton:hover {
            background: #45a049;
        }
    </style>
</head>

<body>
    <button id="startButton">Start Camera</button>

    <div id="videoContainer" style="display: none;">
        <video id="video" autoplay playsinline muted></video>
        <div id="backdrop"></div>
    </div>

    <div class="controls" style="display: none;" id="controls">
        <h3>🎬 Backdrop Controls</h3>

        <div class="control-group">
            <label>Width:</label>
            <input type="range" id="width" min="10" max="90" value="60" step="1">
            <span class="value" id="widthValue">60%</span>
        </div>

        <div class="control-group">
            <label>Height:</label>
            <input type="range" id="height" min="10" max="90" value="70" step="1">
            <span class="value" id="heightValue">70%</span>
        </div>

        <div class="control-group">
            <label>Opacity:</label>
            <input type="range" id="opacity" min="0" max="100" value="80" step="5">
            <span class="value" id="opacityValue">80%</span>
        </div>

        <div class="control-group">
            <label>Border:</label>
            <input type="range" id="border" min="0" max="10" value="3" step="1">
            <span class="value" id="borderValue">3px</span>
        </div>

        <div class="control-group">
            <label>Distance:</label>
            <input type="range" id="distance" min="1" max="100" value="50" step="1">
            <span class="value" id="distanceValue">50</span>
        </div>

        <div class="control-group">
            <label>X Position:</label>
            <input type="range" id="positionX" min="-50" max="50" value="0" step="1">
            <span class="value" id="positionXValue">0</span>
        </div>

        <div class="control-group">
            <label>Y Position:</label>
            <input type="range" id="positionY" min="-50" max="50" value="0" step="1">
            <span class="value" id="positionYValue">0</span>
        </div>

        <div class="button-row">
            <button onclick="toggleColor()">🎨 Color</button>
            <button onclick="toggleBorder()">📐 Border</button>
            <button onclick="fullScreen()">📱 Fill</button>
            <button onclick="resetBackdrop()">🔄 Reset</button>
        </div>

        <div class="button-row">
            <button onclick="setNear()">📍 Near</button>
            <button onclick="setFar()">🌅 Far</button>
            <button onclick="centerBackdrop()">🎯 Center</button>
            <button onclick="moveToBackground()">⬇️ Behind</button>
        </div>

        <div style="margin-top: 10px; font-size: 10px; color: #ccc;">
            <div>Shortcuts: C=Color, F=Fill, R=Reset, N=Near, M=Far</div>
            <div>Arrow Keys: Move backdrop ↑↓←→</div>
        </div>
    </div>

    <div id="error">
        <h3>Camera Error</h3>
        <p id="errorMessage"></p>
        <button onclick="hideError()">OK</button>
    </div>

    <script>
        // Global variables
        let video, backdrop, stream;
        const colors = ['#4CC3D9', '#EF2D5E', '#FFC65D', '#7BC8A4', '#CC7A00', '#8B4A9C', '#FF6B6B'];
        const borderColors = ['#FF6B6B', '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];
        let currentColorIndex = 0;
        let currentBorderIndex = 0;

        // Get DOM elements
        const startButton = document.getElementById('startButton');
        const videoContainer = document.getElementById('videoContainer');
        const controls = document.getElementById('controls');
        const widthSlider = document.getElementById('width');
        const heightSlider = document.getElementById('height');
        const opacitySlider = document.getElementById('opacity');
        const borderSlider = document.getElementById('border');
        const distanceSlider = document.getElementById('distance');
        const positionXSlider = document.getElementById('positionX');
        const positionYSlider = document.getElementById('positionY');
        const widthValue = document.getElementById('widthValue');
        const heightValue = document.getElementById('heightValue');
        const opacityValue = document.getElementById('opacityValue');
        const borderValue = document.getElementById('borderValue');
        const distanceValue = document.getElementById('distanceValue');
        const positionXValue = document.getElementById('positionXValue');
        const positionYValue = document.getElementById('positionYValue');
        const errorDiv = document.getElementById('error');
        const errorMessage = document.getElementById('errorMessage');

        // Start camera function
        async function startCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    },
                    audio: false
                });

                video = document.getElementById('video');
                backdrop = document.getElementById('backdrop');

                video.srcObject = stream;

                video.onloadedmetadata = () => {
                    startButton.style.display = 'none';
                    videoContainer.style.display = 'block';
                    controls.style.display = 'block';
                    updateBackdrop();
                };

            } catch (err) {
                showError('Could not access camera: ' + err.message);
            }
        }

        // Update backdrop appearance
        function updateBackdrop() {
            if (!backdrop) return;

            const width = widthSlider.value;
            const height = heightSlider.value;
            const opacity = opacitySlider.value / 100;
            const borderWidth = borderSlider.value;
            const distance = distanceSlider.value;
            const posX = positionXSlider.value;
            const posY = positionYSlider.value;

            // Calculate scale and blur based on distance
            const scale = calculateScale(distance);
            const blur = calculateBlur(distance);
            const brightness = calculateBrightness(distance);

            // Calculate position offsets for full-screen backdrop
            const xOffset = posX * 2; // Increased range for full screen
            const yOffset = posY * 2;

            // For full-screen backdrop, we use width/height as scale factors
            const scaleX = width / 100;
            const scaleY = height / 100;

            backdrop.style.opacity = opacity;
            backdrop.style.borderWidth = borderWidth + 'px';
            backdrop.style.transform = `translate(${xOffset}px, ${yOffset}px) scale(${scaleX * scale}, ${scaleY * scale})`;
            backdrop.style.filter = `blur(${blur}px) brightness(${brightness}%)`;

            // Update display values
            widthValue.textContent = width + '%';
            heightValue.textContent = height + '%';
            opacityValue.textContent = opacitySlider.value + '%';
            borderValue.textContent = borderWidth + 'px';
            distanceValue.textContent = distance;
            positionXValue.textContent = posX;
            positionYValue.textContent = posY;
        }

        // Calculate scale based on distance (far = smaller, near = larger)
        function calculateScale(distance) {
            // Distance 1 = very close (scale 1.5), Distance 100 = very far (scale 0.3)
            return 1.5 - (distance - 1) * (1.2 / 99);
        }

        // Calculate blur based on distance (simulate depth of field)
        function calculateBlur(distance) {
            // Near objects are sharp, far objects are slightly blurred
            if (distance < 30) return 0; // Sharp for close objects
            return (distance - 30) * 0.05; // Gradual blur for distant objects
        }

        // Calculate brightness based on distance (atmospheric perspective)
        function calculateBrightness(distance) {
            // Far objects appear slightly dimmer
            return Math.max(70, 100 - (distance * 0.3));
        }

        // Toggle backdrop color
        function toggleColor() {
            if (!backdrop) return;
            currentColorIndex = (currentColorIndex + 1) % colors.length;
            backdrop.style.backgroundColor = colors[currentColorIndex];
        }

        // Toggle border color
        function toggleBorder() {
            if (!backdrop) return;
            currentBorderIndex = (currentBorderIndex + 1) % borderColors.length;
            backdrop.style.borderColor = borderColors[currentBorderIndex];
        }

        // Full screen backdrop
        function fullScreen() {
            if (!backdrop) return;
            widthSlider.value = 95;
            heightSlider.value = 95;
            updateBackdrop();
        }

        // Set backdrop to near distance
        function setNear() {
            if (!backdrop) return;
            distanceSlider.value = 15;
            updateBackdrop();
        }

        // Set backdrop to far distance
        function setFar() {
            if (!backdrop) return;
            distanceSlider.value = 85;
            updateBackdrop();
        }

        // Auto-fit backdrop based on distance
        function autoFit() {
            if (!backdrop) return;
            const distance = parseFloat(distanceSlider.value);

            // Adjust size based on distance for realistic perspective
            if (distance < 30) {
                // Close objects appear larger
                widthSlider.value = Math.min(90, 40 + (30 - distance) * 1.5);
                heightSlider.value = Math.min(90, 50 + (30 - distance) * 1.2);
            } else {
                // Far objects need to be larger to be visible
                widthSlider.value = Math.min(95, 60 + (distance - 30) * 0.5);
                heightSlider.value = Math.min(95, 70 + (distance - 30) * 0.4);
            }

            updateBackdrop();
        }

        // Center the backdrop
        function centerBackdrop() {
            if (!backdrop) return;
            positionXSlider.value = 0;
            positionYSlider.value = 0;
            updateBackdrop();
        }

        // Move backdrop to typical "background" position (behind person)
        function moveToBackground() {
            if (!backdrop) return;
            positionXSlider.value = 0;  // Center horizontally
            positionYSlider.value = 0;  // Center vertically
            distanceSlider.value = 90;  // Set to far distance
            widthSlider.value = 100;    // Full width
            heightSlider.value = 100;   // Full height
            opacitySlider.value = 60;   // Reduce opacity so you're visible
            updateBackdrop();
        }

        // Reset backdrop to defaults
        function resetBackdrop() {
            widthSlider.value = 100;
            heightSlider.value = 100;
            opacitySlider.value = 60;
            borderSlider.value = 0;
            distanceSlider.value = 80;
            positionXSlider.value = 0;
            positionYSlider.value = 0;
            currentColorIndex = 0;
            currentBorderIndex = 0;
            if (backdrop) {
                backdrop.style.backgroundColor = colors[0];
                backdrop.style.borderColor = borderColors[0];
            }
            updateBackdrop();
        }

        // Show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorDiv.style.display = 'block';
        }

        // Hide error message
        function hideError() {
            errorDiv.style.display = 'none';
        }

        // Event listeners
        startButton.addEventListener('click', startCamera);
        widthSlider.addEventListener('input', updateBackdrop);
        heightSlider.addEventListener('input', updateBackdrop);
        opacitySlider.addEventListener('input', updateBackdrop);
        borderSlider.addEventListener('input', updateBackdrop);
        distanceSlider.addEventListener('input', updateBackdrop);

        // Keyboard shortcuts
        document.addEventListener('keydown', function (event) {
            if (event.target.tagName === 'INPUT') return; // Don't interfere with input fields

            switch (event.key.toLowerCase()) {
                case 'c':
                    toggleColor();
                    break;
                case 'b':
                    toggleBorder();
                    break;
                case 'f':
                    fullScreen();
                    break;
                case 'r':
                    resetBackdrop();
                    break;
                case 'n':
                    setNear();
                    break;
                case 'm':
                    setFar();
                    break;
                case 'a':
                    autoFit();
                    break;
                case ' ':
                    event.preventDefault();
                    if (backdrop) {
                        backdrop.style.display = backdrop.style.display === 'none' ? 'block' : 'none';
                    }
                    break;
            }
        });

        // Handle window resize
        window.addEventListener('resize', function () {
            if (backdrop) {
                setTimeout(updateBackdrop, 100);
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function () {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
        });
    </script>
</body>

</html>