<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>A-Frame AR Backdrop</title>
    <meta name="description" content="A-Frame AR Backdrop">
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v6.1.1/dist/aframe-extras.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #000;
        }

        #videoContainer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
        }

        #video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: scaleX(-1); /* Mirror the video */
        }

        a-scene {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 2;
            pointer-events: none;
        }

        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            min-width: 220px;
            pointer-events: auto;
        }

        .controls h3 {
            margin: 0 0 10px 0;
            color: #FF6B6B;
        }

        .control-group {
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            min-width: 70px;
            font-size: 11px;
        }

        .control-group input[type="range"] {
            flex: 1;
            height: 20px;
        }

        .control-group .value {
            min-width: 40px;
            text-align: right;
            font-weight: bold;
            color: #FF6B6B;
        }

        .controls button {
            background: #FF6B6B;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.3s;
        }

        .controls button:hover {
            background: #ff5252;
        }

        .button-row {
            display: flex;
            gap: 5px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        #startButton {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #FF6B6B;
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
        }

        #startButton:hover {
            background: #ff5252;
        }

        .environment-selector {
            margin: 10px 0;
        }

        .environment-selector select {
            width: 100%;
            padding: 5px;
            background: #333;
            color: white;
            border: 1px solid #555;
            border-radius: 4px;
        }
    </style>
</head>

<body>
    <button id="startButton">Start AR Backdrop</button>

    <div id="videoContainer" style="display: none;">
        <video id="video" autoplay playsinline muted></video>
    </div>

    <a-scene 
        embedded 
        style="display: none;" 
        id="scene"
        background="color: transparent"
        vr-mode-ui="enabled: false"
        device-orientation-permission-ui="enabled: false">
        
        <a-assets>
            <a-mixin id="backdrop-material" 
                material="color: #4CC3D9; opacity: 0.8; transparent: true; side: double"></a-mixin>
        </a-assets>

        <!-- Camera -->
        <a-camera id="camera" 
            position="0 1.6 0" 
            look-controls="enabled: false" 
            wasd-controls="enabled: false">
        </a-camera>

        <!-- Main backdrop plane -->
        <a-plane id="backdrop" 
            mixin="backdrop-material"
            position="0 1.6 -5" 
            width="10" 
            height="6"
            animation__hover="property: rotation; to: 0 5 0; dur: 2000; loop: true; dir: alternate">
        </a-plane>

        <!-- Environment elements -->
        <a-sky id="sky" color="#001122" opacity="0.3"></a-sky>
        
        <!-- Lighting -->
        <a-light type="ambient" color="#404040" intensity="0.4"></a-light>
        <a-light type="directional" position="0 10 5" color="#ffffff" intensity="0.6"></a-light>
    </a-scene>

    <div class="controls" style="display: none;" id="controls">
        <h3>🎬 A-Frame AR Backdrop</h3>

        <div class="environment-selector">
            <label>Environment:</label>
            <select id="environmentSelect">
                <option value="simple">Simple Backdrop</option>
                <option value="room">Virtual Room</option>
                <option value="nature">Nature Scene</option>
                <option value="space">Space Environment</option>
                <option value="studio">Studio Setup</option>
            </select>
        </div>

        <div class="control-group">
            <label>Distance:</label>
            <input type="range" id="distance" min="2" max="20" value="5" step="0.5">
            <span class="value" id="distanceValue">5m</span>
        </div>

        <div class="control-group">
            <label>Width:</label>
            <input type="range" id="width" min="2" max="30" value="10" step="0.5">
            <span class="value" id="widthValue">10m</span>
        </div>

        <div class="control-group">
            <label>Height:</label>
            <input type="range" id="height" min="2" max="20" value="6" step="0.5">
            <span class="value" id="heightValue">6m</span>
        </div>

        <div class="control-group">
            <label>Opacity:</label>
            <input type="range" id="opacity" min="0" max="100" value="80" step="5">
            <span class="value" id="opacityValue">80%</span>
        </div>

        <div class="control-group">
            <label>X Position:</label>
            <input type="range" id="positionX" min="-10" max="10" value="0" step="0.5">
            <span class="value" id="positionXValue">0</span>
        </div>

        <div class="control-group">
            <label>Y Position:</label>
            <input type="range" id="positionY" min="-5" max="5" value="1.6" step="0.1">
            <span class="value" id="positionYValue">1.6</span>
        </div>

        <div class="button-row">
            <button onclick="toggleColor()">🎨 Color</button>
            <button onclick="toggleAnimation()">✨ Animate</button>
            <button onclick="resetBackdrop()">🔄 Reset</button>
        </div>

        <div class="button-row">
            <button onclick="setPreset('close')">📍 Close</button>
            <button onclick="setPreset('far')">🌅 Far</button>
            <button onclick="setPreset('wide')">📐 Wide</button>
            <button onclick="setPreset('tall')">📏 Tall</button>
        </div>

        <div style="margin-top: 10px; font-size: 10px; color: #ccc;">
            <div>A-Frame 3D Backdrop System</div>
            <div>Shortcuts: C=Color, A=Animate, R=Reset</div>
        </div>
    </div>

    <script>
        // Global variables
        let video, scene, backdrop, camera;
        let stream;
        const colors = ['#4CC3D9', '#EF2D5E', '#FFC65D', '#7BC8A4', '#CC7A00', '#8B4A9C', '#FF6B6B'];
        let currentColorIndex = 0;
        let animationEnabled = false;

        // Get DOM elements
        const startButton = document.getElementById('startButton');
        const videoContainer = document.getElementById('videoContainer');
        const controls = document.getElementById('controls');
        const sceneEl = document.getElementById('scene');

        // Control elements
        const environmentSelect = document.getElementById('environmentSelect');
        const distanceSlider = document.getElementById('distance');
        const widthSlider = document.getElementById('width');
        const heightSlider = document.getElementById('height');
        const opacitySlider = document.getElementById('opacity');
        const positionXSlider = document.getElementById('positionX');
        const positionYSlider = document.getElementById('positionY');

        // Value display elements
        const distanceValue = document.getElementById('distanceValue');
        const widthValue = document.getElementById('widthValue');
        const heightValue = document.getElementById('heightValue');
        const opacityValue = document.getElementById('opacityValue');
        const positionXValue = document.getElementById('positionXValue');
        const positionYValue = document.getElementById('positionYValue');

        // Start camera and A-Frame scene
        async function startCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    },
                    audio: false
                });

                video = document.getElementById('video');
                video.srcObject = stream;

                video.onloadedmetadata = () => {
                    startButton.style.display = 'none';
                    videoContainer.style.display = 'block';
                    sceneEl.style.display = 'block';
                    controls.style.display = 'block';
                    
                    // Initialize A-Frame elements
                    initAFrame();
                };

            } catch (err) {
                alert('Could not access camera: ' + err.message);
            }
        }

        // Initialize A-Frame elements
        function initAFrame() {
            scene = document.querySelector('a-scene');
            backdrop = document.getElementById('backdrop');
            camera = document.getElementById('camera');
            
            // Wait for scene to load
            if (scene.hasLoaded) {
                setupScene();
            } else {
                scene.addEventListener('loaded', setupScene);
            }
        }

        function setupScene() {
            updateBackdrop();
            setupEventListeners();
        }

        // Setup event listeners
        function setupEventListeners() {
            distanceSlider.addEventListener('input', updateBackdrop);
            widthSlider.addEventListener('input', updateBackdrop);
            heightSlider.addEventListener('input', updateBackdrop);
            opacitySlider.addEventListener('input', updateBackdrop);
            positionXSlider.addEventListener('input', updateBackdrop);
            positionYSlider.addEventListener('input', updateBackdrop);
            environmentSelect.addEventListener('change', changeEnvironment);
        }

        // Update backdrop properties
        function updateBackdrop() {
            if (!backdrop) return;

            const distance = parseFloat(distanceSlider.value);
            const width = parseFloat(widthSlider.value);
            const height = parseFloat(heightSlider.value);
            const opacity = parseFloat(opacitySlider.value) / 100;
            const posX = parseFloat(positionXSlider.value);
            const posY = parseFloat(positionYSlider.value);

            // Update backdrop position and size
            backdrop.setAttribute('position', `${posX} ${posY} ${-distance}`);
            backdrop.setAttribute('width', width);
            backdrop.setAttribute('height', height);
            backdrop.setAttribute('material', `opacity: ${opacity}; transparent: true; side: double`);

            // Update display values
            distanceValue.textContent = distance + 'm';
            widthValue.textContent = width + 'm';
            heightValue.textContent = height + 'm';
            opacityValue.textContent = Math.round(opacity * 100) + '%';
            positionXValue.textContent = posX;
            positionYValue.textContent = posY;
        }

        // Change environment
        function changeEnvironment() {
            const environment = environmentSelect.value;
            const sky = document.getElementById('sky');

            // Remove existing environment entities
            const existingEnv = document.querySelectorAll('.environment-entity');
            existingEnv.forEach(entity => entity.remove());

            switch (environment) {
                case 'simple':
                    sky.setAttribute('color', '#001122');
                    sky.setAttribute('opacity', '0.3');
                    break;

                case 'room':
                    createRoom();
                    sky.setAttribute('color', '#f0f0f0');
                    sky.setAttribute('opacity', '0.1');
                    break;

                case 'nature':
                    createNature();
                    sky.setAttribute('color', '#87CEEB');
                    sky.setAttribute('opacity', '0.6');
                    break;

                case 'space':
                    createSpace();
                    sky.setAttribute('color', '#000011');
                    sky.setAttribute('opacity', '0.9');
                    break;

                case 'studio':
                    createStudio();
                    sky.setAttribute('color', '#2a2a2a');
                    sky.setAttribute('opacity', '0.8');
                    break;
            }
        }

        // Create room environment
        function createRoom() {
            const scene = document.querySelector('a-scene');

            // Floor
            const floor = document.createElement('a-plane');
            floor.setAttribute('class', 'environment-entity');
            floor.setAttribute('position', '0 0 -8');
            floor.setAttribute('rotation', '-90 0 0');
            floor.setAttribute('width', '20');
            floor.setAttribute('height', '20');
            floor.setAttribute('material', 'color: #8B4513; opacity: 0.7');
            scene.appendChild(floor);

            // Walls
            const backWall = document.createElement('a-plane');
            backWall.setAttribute('class', 'environment-entity');
            backWall.setAttribute('position', '0 2 -15');
            backWall.setAttribute('width', '20');
            backWall.setAttribute('height', '8');
            backWall.setAttribute('material', 'color: #DEB887; opacity: 0.8');
            scene.appendChild(backWall);
        }

        // Create nature environment
        function createNature() {
            const scene = document.querySelector('a-scene');

            // Ground
            const ground = document.createElement('a-plane');
            ground.setAttribute('class', 'environment-entity');
            ground.setAttribute('position', '0 0 -10');
            ground.setAttribute('rotation', '-90 0 0');
            ground.setAttribute('width', '30');
            ground.setAttribute('height', '30');
            ground.setAttribute('material', 'color: #228B22; opacity: 0.6');
            scene.appendChild(ground);

            // Trees
            for (let i = 0; i < 5; i++) {
                const tree = document.createElement('a-cylinder');
                tree.setAttribute('class', 'environment-entity');
                tree.setAttribute('position', `${(Math.random() - 0.5) * 20} 2 ${-8 - Math.random() * 10}`);
                tree.setAttribute('radius', '0.3');
                tree.setAttribute('height', '4');
                tree.setAttribute('material', 'color: #8B4513; opacity: 0.7');
                scene.appendChild(tree);
            }
        }

        // Create space environment
        function createSpace() {
            const scene = document.querySelector('a-scene');

            // Stars
            for (let i = 0; i < 20; i++) {
                const star = document.createElement('a-sphere');
                star.setAttribute('class', 'environment-entity');
                star.setAttribute('position', `${(Math.random() - 0.5) * 40} ${Math.random() * 20} ${-5 - Math.random() * 30}`);
                star.setAttribute('radius', '0.1');
                star.setAttribute('material', 'color: white; emissive: white; emissiveIntensity: 0.5');
                scene.appendChild(star);
            }

            // Planet
            const planet = document.createElement('a-sphere');
            planet.setAttribute('class', 'environment-entity');
            planet.setAttribute('position', '8 3 -15');
            planet.setAttribute('radius', '2');
            planet.setAttribute('material', 'color: #FF6347; opacity: 0.8');
            scene.appendChild(planet);
        }

        // Create studio environment
        function createStudio() {
            const scene = document.querySelector('a-scene');

            // Studio lights
            for (let i = 0; i < 3; i++) {
                const light = document.createElement('a-light');
                light.setAttribute('class', 'environment-entity');
                light.setAttribute('type', 'spot');
                light.setAttribute('position', `${(i - 1) * 5} 5 -3`);
                light.setAttribute('color', '#ffffff');
                light.setAttribute('intensity', '0.5');
                scene.appendChild(light);
            }
        }

        // Toggle backdrop color
        function toggleColor() {
            if (!backdrop) return;
            currentColorIndex = (currentColorIndex + 1) % colors.length;
            const currentMaterial = backdrop.getAttribute('material');
            backdrop.setAttribute('material', `color: ${colors[currentColorIndex]}; opacity: ${currentMaterial.opacity}; transparent: true; side: double`);
        }

        // Toggle animation
        function toggleAnimation() {
            if (!backdrop) return;
            animationEnabled = !animationEnabled;

            if (animationEnabled) {
                backdrop.setAttribute('animation__rotation', 'property: rotation; to: 0 360 0; dur: 10000; loop: true');
                backdrop.setAttribute('animation__scale', 'property: scale; to: 1.1 1.1 1.1; dur: 3000; loop: true; dir: alternate');
            } else {
                backdrop.removeAttribute('animation__rotation');
                backdrop.removeAttribute('animation__scale');
                backdrop.setAttribute('rotation', '0 0 0');
                backdrop.setAttribute('scale', '1 1 1');
            }
        }

        // Set presets
        function setPreset(preset) {
            if (!backdrop) return;

            switch (preset) {
                case 'close':
                    distanceSlider.value = 3;
                    widthSlider.value = 6;
                    heightSlider.value = 4;
                    break;
                case 'far':
                    distanceSlider.value = 15;
                    widthSlider.value = 20;
                    heightSlider.value = 12;
                    break;
                case 'wide':
                    distanceSlider.value = 8;
                    widthSlider.value = 25;
                    heightSlider.value = 6;
                    break;
                case 'tall':
                    distanceSlider.value = 8;
                    widthSlider.value = 8;
                    heightSlider.value = 15;
                    break;
            }
            updateBackdrop();
        }

        // Reset backdrop
        function resetBackdrop() {
            distanceSlider.value = 5;
            widthSlider.value = 10;
            heightSlider.value = 6;
            opacitySlider.value = 80;
            positionXSlider.value = 0;
            positionYSlider.value = 1.6;
            environmentSelect.value = 'simple';
            currentColorIndex = 0;
            animationEnabled = false;

            if (backdrop) {
                backdrop.removeAttribute('animation__rotation');
                backdrop.removeAttribute('animation__scale');
                backdrop.setAttribute('rotation', '0 0 0');
                backdrop.setAttribute('scale', '1 1 1');
            }

            changeEnvironment();
            updateBackdrop();
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function (event) {
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') return;

            switch (event.key.toLowerCase()) {
                case 'c':
                    toggleColor();
                    break;
                case 'a':
                    toggleAnimation();
                    break;
                case 'r':
                    resetBackdrop();
                    break;
                case '1':
                    setPreset('close');
                    break;
                case '2':
                    setPreset('far');
                    break;
                case '3':
                    setPreset('wide');
                    break;
                case '4':
                    setPreset('tall');
                    break;
            }
        });

        // Event listeners
        startButton.addEventListener('click', startCamera);

        // Cleanup on page unload
        window.addEventListener('beforeunload', function () {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
        });
    </script>
</body>
</html>
